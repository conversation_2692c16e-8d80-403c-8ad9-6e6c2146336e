import React, { useState, useEffect, useRef } from "react";

// ----------------- Icons -----------------
const IconMic = ({ className }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
    <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
    <line x1="12" y1="19" x2="12" y2="22"></line>
  </svg>
);

const IconLoader = ({ className }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <line x1="12" y1="2" x2="12" y2="6" />
    <line x1="12" y1="18" x2="12" y2="22" />
    <line x1="4.93" y1="4.93" x2="7.76" y2="7.76" />
    <line x1="16.24" y1="16.24" x2="19.07" y2="19.07" />
    <line x1="2" y1="12" x2="6" y2="12" />
    <line x1="18" y1="12" x2="22" y2="12" />
    <line x1="4.93" y1="19.07" x2="7.76" y2="16.24" />
    <line x1="16.24" y1="7.76" x2="19.07" y2="4.93" />
  </svg>
);

const IconImage = ({ className }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <rect width="18" height="18" x="3" y="3" rx="2" ry="2"/>
    <circle cx="9" cy="9" r="2"/>
    <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
  </svg>
);

const IconSend = ({ className }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="m22 2-7 20-4-9-9-4Z"/>
    <path d="M22 2 11 13"/>
  </svg>
);

const IconSettings = ({ className }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/>
    <circle cx="12" cy="12" r="3"/>
  </svg>
);



// ----------------- Main Component -----------------
export default function App() {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [conversation, setConversation] = useState([]);
  const [error, setError] = useState(null);
  const [isTTSEnabled, setIsTTSEnabled] = useState(true);

  // image-related state
  const [imageFile, setImageFile] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [imagePrompt, setImagePrompt] = useState("");

  // audio recording refs
  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);

  // ----------------- AUDIO RECORDING -----------------
  const startRecording = async () => {
    setError(null);
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaRecorderRef.current = new MediaRecorder(stream);
      audioChunksRef.current = [];
      mediaRecorderRef.current.ondataavailable = (e) => audioChunksRef.current.push(e.data);
      mediaRecorderRef.current.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: "audio/wav" });
        await processAudio(audioBlob);
        stream.getTracks().forEach((t) => t.stop());
      };
      mediaRecorderRef.current.start();
      setIsRecording(true);
      setTimeout(stopRecording, 4000);
    } catch (err) {
      setError("Could not access microphone.");
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current?.state === "recording") {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      setIsProcessing(true);
    }
  };

  const blobToBase64 = (blob) =>
    new Promise((res, rej) => {
      const reader = new FileReader();
      reader.onloadend = () => res(reader.result.split(",")[1]);
      reader.onerror = rej;
      reader.readAsDataURL(blob);
    });

  const processAudio = async (audioBlob) => {
    setIsProcessing(true);
    try {
      const b64 = await blobToBase64(audioBlob);
      setConversation((p) => [...p, { role: "user", parts: [{ type: "audio" }] }]);
      const res = await fetch("http://localhost:8000/ask", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ data: b64 }),
      });
      if (!res.ok) throw new Error(await res.text());
      const { text } = await res.json();
      addReply(text);
    } catch (e) {
      setError(e.message);
    } finally {
      setIsProcessing(false);
    }
  };

  // ----------------- IMAGE + PROMPT -----------------
  const submitImageQuestion = async () => {
    if (!imageFile || !imagePrompt.trim()) return;
    setIsProcessing(true);
    setError(null);
    try {
      setConversation((p) => [
        ...p,
        { role: "user", parts: [{ type: "image", url: imagePreview }, { type: "text", text: imagePrompt }] },
      ]);
      const form = new FormData();
      form.append("prompt", imagePrompt);
      form.append("image", imageFile, imageFile.name);

      const res = await fetch("http://localhost:8000/ask_image", { method: "POST", body: form });
      if (!res.ok) throw new Error(await res.text());
      const { text } = await res.json();
      addReply(text);
    } catch (e) {
      setError(e.message);
    } finally {
      setIsProcessing(false);
      setImageFile(null);
      setImagePreview(null);
      setImagePrompt("");
    }
  };

  // ----------------- TTS -----------------
  const addReply = (text) => {
    setConversation((p) => [...p, { role: "model", parts: [{ text }] }]);
    if (isTTSEnabled && "speechSynthesis" in window) {
      const utter = new SpeechSynthesisUtterance(text.replace(/[-•▪●◦—–]/g, "-"));
      window.speechSynthesis.cancel();
      window.speechSynthesis.speak(utter);
    }
  };

  // ----------------- Scroll to bottom -----------------
  const bottomRef = useRef(null);
  useEffect(() => bottomRef.current?.scrollIntoView({ behavior: "smooth" }), [conversation]);

  // ----------------- RENDER -----------------
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white">
      {/* Animated background */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -inset-10 opacity-50">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
          <div className="absolute top-1/3 right-1/4 w-96 h-96 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
          <div className="absolute bottom-1/4 left-1/3 w-96 h-96 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
        </div>
      </div>

      <div className="relative z-10 flex flex-col h-screen">
        {/* Modern Header */}
        <header className="backdrop-blur-xl bg-white/10 border-b border-white/20 p-6">
          <div className="max-w-4xl mx-auto flex justify-between items-center">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                <IconMic className="w-6 h-6" />
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                  Gemma-3n Assistant
                </h1>
                <p className="text-sm text-gray-400">Multi-modal AI powered by voice, text & vision</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <label className="flex items-center space-x-2 bg-white/10 rounded-full px-4 py-2 backdrop-blur-sm">
                <IconSettings className="w-4 h-4" />
                <span className="text-sm">TTS</span>
                <input
                  type="checkbox"
                  checked={isTTSEnabled}
                  onChange={() => setIsTTSEnabled(!isTTSEnabled)}
                  className="w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500"
                />
              </label>
            </div>
          </div>
        </header>

        {/* Messages Container */}
        <main className="flex-1 overflow-y-auto px-6 py-8">
          <div className="max-w-4xl mx-auto space-y-6">
            {conversation.length === 0 && (
              <div className="text-center py-20">
                <div className="relative">
                  <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center animate-pulse-slow">
                    <IconMic className="w-12 h-12 text-white" />
                  </div>
                  <div className="absolute inset-0 w-24 h-24 mx-auto bg-gradient-to-r from-purple-500 to-pink-500 rounded-full opacity-20 animate-ping"></div>
                </div>
                <h2 className="text-2xl font-semibold mb-2 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                  Ready to Chat
                </h2>
                <p className="text-gray-400 max-w-md mx-auto">
                  Press the microphone to start a voice conversation, or upload an image to ask questions about it.
                </p>
              </div>
            )}

            {conversation.map((msg, i) => (
              <div key={i} className={`flex ${msg.role === "user" ? "justify-end" : "justify-start"} mb-4`}>
                <div className={`chat-bubble ${msg.role === "user" ? "chat-bubble-user" : "chat-bubble-assistant"} backdrop-blur-sm shadow-lg`}>
                  {msg.parts[0].type === "audio" ? (
                    <div className="flex items-center space-x-2">
                      <IconMic className="w-4 h-4 opacity-70" />
                      <p className="italic text-sm">Voice message</p>
                    </div>
                  ) : msg.parts[0].type === "image" ? (
                    <div>
                      <img src={msg.parts[0].url} alt="upload" className="max-w-xs mb-3 rounded-lg shadow-md" />
                      <p className="text-sm">{msg.parts[1].text}</p>
                    </div>
                  ) : (
                    <p className="text-sm leading-relaxed">{msg.parts[0].text}</p>
                  )}
                </div>
              </div>
            ))}
            <div ref={bottomRef} />
          </div>
        </main>

        {/* Image Upload Section */}
        <section className="px-6 pb-6">
          <div className="max-w-4xl mx-auto space-y-4">
            <input
              type="file"
              accept="image/*"
              id="img-input"
              className="hidden"
              onChange={(e) => {
                const file = e.target.files[0];
                if (!file) return;
                setImageFile(file);
                setImagePreview(URL.createObjectURL(file));
              }}
            />
            <label
              htmlFor="img-input"
              className="block w-full border-2 border-dashed border-white/30 text-center p-8 rounded-2xl cursor-pointer backdrop-blur-sm bg-white/5 hover:bg-white/10 transition-all duration-300 group"
            >
              {imagePreview ? (
                <div className="relative">
                  <img src={imagePreview} alt="preview" className="mx-auto max-h-48 rounded-xl shadow-lg" />
                  <div className="absolute inset-0 bg-black/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                    <IconImage className="w-8 h-8 text-white" />
                  </div>
                </div>
              ) : (
                <div className="py-8">
                  <IconImage className="w-12 h-12 mx-auto mb-4 text-gray-400 group-hover:text-white transition-colors" />
                  <p className="text-gray-400 group-hover:text-white transition-colors">
                    Click or drag an image here to analyze
                  </p>
                </div>
              )}
            </label>

            {imagePreview && (
              <div className="space-y-4">
                <textarea
                  value={imagePrompt}
                  onChange={(e) => setImagePrompt(e.target.value)}
                  placeholder="Ask a question about the image…"
                  className="w-full bg-white/10 backdrop-blur-sm border border-white/20 p-4 rounded-xl resize-none text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  rows="3"
                />
                <button
                  onClick={submitImageQuestion}
                  disabled={!imagePrompt.trim() || isProcessing}
                  className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 py-3 rounded-xl font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 flex items-center justify-center space-x-2"
                >
                  <IconSend className="w-4 h-4" />
                  <span>Analyze Image</span>
                </button>
              </div>
            )}
          </div>
        </section>

        {/* Modern Footer with Mic Button */}
        <footer className="p-6 backdrop-blur-xl bg-white/10 border-t border-white/20">
          <div className="max-w-4xl mx-auto flex flex-col items-center space-y-4">
            {error && (
              <div className="bg-red-500/20 border border-red-500/30 text-red-200 px-4 py-2 rounded-lg backdrop-blur-sm">
                {error}
              </div>
            )}

            <div className="relative">
              <button
                onClick={startRecording}
                disabled={isRecording || isProcessing}
                className="w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 rounded-full flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95"
              >
                {isRecording ? (
                  <div className="w-8 h-8 bg-red-500 rounded-sm animate-pulse" />
                ) : isProcessing ? (
                  <IconLoader className="w-8 h-8 animate-spin text-white" />
                ) : (
                  <IconMic className="w-8 h-8 text-white" />
                )}
              </button>

              {isRecording && (
                <div className="absolute inset-0 w-20 h-20 bg-red-500 rounded-full opacity-30 animate-ping"></div>
              )}
            </div>

            <div className="text-center">
              <p className="text-sm font-medium text-white">
                {isRecording ? "Recording..." : isProcessing ? "Processing..." : "Tap to speak"}
              </p>
              <p className="text-xs text-gray-400 mt-1">
                {!isRecording && !isProcessing && "4 second recording"}
              </p>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
}
