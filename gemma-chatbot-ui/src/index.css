@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-200;
  }
  body {
    @apply bg-slate-900 text-white;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .glass-effect {
    @apply backdrop-blur-xl bg-white/10 border border-white/20;
  }

  .gradient-bg {
    background: linear-gradient(-45deg, #667eea 0%, #764ba2 100%);
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
  }

  .chat-bubble {
    @apply relative px-4 py-3 rounded-2xl max-w-xs sm:max-w-md;
  }

  .chat-bubble-user {
    @apply bg-gradient-to-r from-blue-500 to-purple-600 text-white ml-auto;
  }

  .chat-bubble-assistant {
    @apply bg-gradient-to-r from-gray-700/80 to-gray-800/80 text-white mr-auto border border-white/10;
  }
}

@layer utilities {
  .animate-blob {
    animation: blob 7s infinite;
  }

  .animation-delay-2000 {
    animation-delay: 2s;
  }

  .animation-delay-4000 {
    animation-delay: 4s;
  }
}

@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

@keyframes gradient {
  0%, 100% {
    background-size: 200% 200%;
    background-position: left center;
  }
  50% {
    background-size: 200% 200%;
    background-position: right center;
  }
}
