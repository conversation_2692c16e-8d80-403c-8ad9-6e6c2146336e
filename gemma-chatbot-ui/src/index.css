@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .glass-effect {
    @apply backdrop-blur-xl bg-white/10 border border-white/20;
  }

  .gradient-bg {
    background: linear-gradient(-45deg, #667eea 0%, #764ba2 100%);
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
  }

  .chat-bubble {
    @apply relative px-4 py-3 rounded-2xl max-w-xs sm:max-w-md;
  }

  .chat-bubble::before {
    content: '';
    @apply absolute w-0 h-0;
  }

  .chat-bubble-user {
    @apply bg-gradient-to-r from-blue-500 to-purple-600 text-white ml-auto;
  }

  .chat-bubble-user::before {
    @apply border-l-8 border-l-transparent border-r-8 border-r-blue-500 border-t-8 border-t-transparent border-b-8 border-b-transparent;
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
  }

  .chat-bubble-assistant {
    @apply bg-gradient-to-r from-gray-700 to-gray-800 text-white mr-auto;
  }

  .chat-bubble-assistant::before {
    @apply border-l-8 border-l-gray-700 border-r-8 border-r-transparent border-t-8 border-t-transparent border-b-8 border-b-transparent;
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
  }
}
