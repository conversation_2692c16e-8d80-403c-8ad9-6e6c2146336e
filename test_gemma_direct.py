#!/usr/bin/env python3
"""
Direct test script for Gemma model functionality
Tests audio and image processing without API calls
"""

import os
import base64
import io
from PIL import Image
import torch
import numpy as np

# Import the functions from gemma_record_gui
try:
    from gemma_record_gui import get_model_and_processor, sanitize
    print("✅ Successfully imported gemma_record_gui functions")
except ImportError as e:
    print(f"❌ Failed to import gemma_record_gui: {e}")
    exit(1)

def test_model_loading():
    """Test if the model loads correctly"""
    print("\n=== Testing Model Loading ===")
    try:
        model, processor = get_model_and_processor()
        print("✅ Model and processor loaded successfully")
        print(f"Model type: {type(model)}")
        print(f"Processor type: {type(processor)}")
        return model, processor
    except Exception as e:
        print(f"❌ Failed to load model: {e}")
        return None, None

def test_audio_processing(model, processor, audio_file="नेपाल_audio.wav"):
    """Test audio processing directly"""
    print(f"\n=== Testing Audio Processing: {audio_file} ===")
    
    if not os.path.exists(audio_file):
        print(f"❌ Audio file {audio_file} not found")
        return
    
    try:
        # Read and encode audio file
        with open(audio_file, "rb") as f:
            audio_data = f.read()
        
        audio_b64 = base64.b64encode(audio_data).decode()
        print(f"✅ Audio file read and encoded (size: {len(audio_data)} bytes)")
        
        # Process audio using the same logic as the server
        try:
            import librosa
            print("✅ librosa is available")
        except ImportError:
            print("❌ librosa not available")
            return
        
        # Decode base64 and process
        audio_bytes = base64.b64decode(audio_b64)
        audio_array, sr = librosa.load(io.BytesIO(audio_bytes), sr=16000)
        print(f"✅ Audio decoded with librosa (shape: {audio_array.shape}, sr: {sr})")
        
        # Process with the model (audio needs text prompt)
        inputs = processor(text="Transcribe this audio:", audio=audio_array, sampling_rate=sr, return_tensors="pt")
        print(f"✅ Audio processed by processor (input keys: {list(inputs.keys())})")

        # Generate response with device-aware processing
        with torch.no_grad():
            try:
                # Let the model handle device placement automatically
                generated_ids = model.generate(**inputs, max_new_tokens=128, do_sample=False)
                response = processor.batch_decode(generated_ids, skip_special_tokens=True)[0]
                clean_response = sanitize(response)
            except Exception as e:
                print(f"⚠️  Generation failed: {e}")
                print("Trying with reduced parameters...")
                generated_ids = model.generate(**inputs, max_new_tokens=64, do_sample=False, use_cache=False)
                response = processor.batch_decode(generated_ids, skip_special_tokens=True)[0]
                clean_response = sanitize(response)
        
        print(f"✅ Generated response: {clean_response}")
        return clean_response
        
    except Exception as e:
        print(f"❌ Audio processing failed: {e}")
        import traceback
        traceback.print_exc()

def test_image_processing(model, processor, image_file="image.png", prompt="What do you see in this image?"):
    """Test image processing directly"""
    print(f"\n=== Testing Image Processing: {image_file} ===")
    
    if not os.path.exists(image_file):
        print(f"❌ Image file {image_file} not found")
        return
    
    try:
        # Load image
        image = Image.open(image_file).convert("RGB")
        print(f"✅ Image loaded (size: {image.size})")
        
        # Process with the model
        inputs = processor(text=prompt, images=image, return_tensors="pt")
        print(f"✅ Image and prompt processed (input keys: {list(inputs.keys())})")

        # Generate response with memory-efficient processing
        with torch.no_grad():
            # Clear GPU cache first
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            try:
                generated_ids = model.generate(**inputs, max_new_tokens=64, do_sample=False)
                response = processor.batch_decode(generated_ids, skip_special_tokens=True)[0]
                clean_response = sanitize(response)
            except Exception as e:
                print(f"⚠️  Generation failed: {e}")
                print("Trying with minimal parameters...")
                generated_ids = model.generate(**inputs, max_new_tokens=32, do_sample=False, use_cache=False)
                response = processor.batch_decode(generated_ids, skip_special_tokens=True)[0]
                clean_response = sanitize(response)
        
        print(f"✅ Generated response: {clean_response}")
        return clean_response
        
    except Exception as e:
        print(f"❌ Image processing failed: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main test function"""
    print("🚀 Starting Gemma Model Direct Tests")
    
    # Test model loading
    model, processor = test_model_loading()
    if model is None or processor is None:
        print("❌ Cannot proceed without model and processor")
        return
    
    # Test audio processing
    test_audio_processing(model, processor)
    
    # Test image processing
    test_image_processing(model, processor)
    
    print("\n🎉 All tests completed!")

if __name__ == "__main__":
    main()
